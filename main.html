<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceA - Voice Transcription Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'SF Pro Display', Roboto, sans-serif;
            background: #fafafa;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
            overflow: hidden;
            zoom: 1.0;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Disable zoom and prevent scaling */
        body {
            transform-origin: 0 0;
            transform: scale(1);
        }

        /* Prevent text selection on UI elements */
        .sidebar, .nav-item, .btn-secondary, .action-btn, .upgrade-btn {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Allow text selection only in content areas */
        .activity-text, .activity-silent, input, textarea {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* Layout Structure */
        .app-layout {
            display: flex;
            flex-direction: row;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
        }

        .sidebar {
            width: 240px;
            min-width: 240px;
            max-width: 240px;
<<<<<<< HEAD
            background: #ffffff;
=======
            background: #f8f9fa;
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            height: 100vh;
            overflow: hidden; /* Sidebar should not scroll */
            position: fixed;
            left: 0;
            top: 0;
            z-index: 100;
        }

        .main-content {
            flex: 1;
            background: white;
            overflow-y: auto; /* Only main content scrolls */
            overflow-x: hidden;
            height: 100vh;
            margin-left: 240px; /* Push content to the right of sidebar */
            width: calc(100vw - 240px);
            position: relative;
        }

        .container {
            max-width: none;
            margin: 0;
            padding: 40px 32px 60px 32px; /* Extra bottom padding for scroll space */
            background: white;
            min-height: calc(100vh - 80px); /* Ensure content can extend beyond viewport */
            box-sizing: border-box;
        }

        /* Header Styles */
        .header {
            margin-bottom: 40px;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
<<<<<<< HEAD
            margin: 0;
        }

        /* New Workspace Header Styles */
        .workspace-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .workspace-info {
            flex: 1;
        }

        .workspace-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }



        /* Stats Cards Styles */
        .stats-cards {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            align-items: stretch;
        }

        .stat-card {
            flex: 1;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .stat-icon {
            width: 20px;
            height: 20px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            line-height: 1;
        }

        .stat-value-with-circle {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .usage-text {
            display: flex;
            flex-direction: column;
        }

        .usage-text span:first-child {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            line-height: 1;
        }

        .stat-unit {
            font-size: 14px;
            color: #6c757d;
            font-weight: 400;
            margin-left: 4px;
        }

        .stat-card-nav {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 50px;
        }

        .nav-arrow {
            width: 40px;
            height: 40px;
            border: 1px solid #e9ecef;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-arrow:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
        }

        /* Token Usage Progress Bar Styles - With Circular Progress */
        .usage-progress-container {
            margin: 12px 0 16px 0;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .usage-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .circular-progress-container {
            flex-shrink: 0;
        }

        .circular-progress {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle-bg {
            fill: none;
            stroke: #e9ecef;
            stroke-width: 4;
        }

        .progress-ring-circle {
            fill: none;
            stroke: #28a745;
            stroke-width: 4;
            stroke-linecap: round;
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            transition: stroke-dashoffset 0.8s ease, stroke 0.3s ease;
        }

        .progress-ring-circle.warning {
            stroke: #ffc107;
        }

        .progress-ring-circle.danger {
            stroke: #dc3545;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            color: #495057;
        }

        .usage-details {
            flex: 1;
        }

        .usage-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .usage-label {
            font-size: 13px;
            font-weight: 500;
            color: #495057;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #28a745;
            border-radius: 2px;
            transition: width 0.8s ease, background-color 0.3s ease;
            width: 0%;
        }

        .progress-fill.warning {
            background: #ffc107;
        }

        .progress-fill.danger {
            background: #dc3545;
        }

        .usage-message {
            font-size: 11px;
            color: #6c757d;
        }

        .usage-message.expired {
            color: #dc3545;
            font-weight: 500;
=======
            margin: 0 0 16px 0;
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        }

        .stats-bar {
            display: flex;
            gap: 24px;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .stat-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .stat-icon.orange { background: #ff8c00; }
        .stat-icon.red { background: #ff4757; }
        .stat-icon.yellow { background: #ffa502; }

        /* Main Content Card */
        .main-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 8px 0;
        }

        .card-subtitle {
            font-size: 14px;
            color: #666;
            margin: 0 0 16px 0;
        }

        .hotkey-display {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .key {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 500;
        }

        .hotkey-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hotkey {
            font-size: 1.1rem;
            font-weight: 600;
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }

        .hotkey-desc {
            font-size: 0.95rem;
            opacity: 0.8;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin: 1.5rem 0;
            font-size: 1rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ecdc4;
            animation: blink 1.5s infinite;
        }

        .status-dot.disabled {
            background: #ff6b6b;
            animation: none;
        }

        .status-dot.authenticated {
            background: #4ecdc4;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            font-size: 0.85rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        button {
            padding: 15px 30px;
            border: none;
<<<<<<< HEAD
            border-radius: 0px;
=======
            border-radius: 25px;
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 500;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        button.primary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #5fd3d0, #4fb3a6);
        }

        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.6;
        }

        .minimize-hint {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 0.8rem;
            opacity: 0.6;
            background: rgba(0, 0, 0, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        /* Loading Styles */
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            width: 100vw;
            text-align: center;
            background: #fafbfc;
            position: fixed;
            top: 0;
            left: 0;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            overflow: hidden;
        }

        .loading-container .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 700;
            margin: 0 auto 32px auto;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
            }
        }

        .loading-container h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0 auto 12px auto;
            text-align: center;
        }

        .loading-container .subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 0 auto 40px auto;
            max-width: 400px;
            line-height: 1.5;
            text-align: center;
        }

        .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 24px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-status {
            font-size: 14px;
            color: #9ca3af;
            margin: 16px auto 0 auto;
            font-weight: 500;
            text-align: center;
        }

        .loading-spinner {
            margin: 2rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-status {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 1rem;
            color: #4ecdc4;
        }

        /* Login Styles */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            width: 100vw;
            background: #fafbfc;
            padding: 40px 20px;
            position: fixed;
            top: 0;
            left: 0;
            box-sizing: border-box;
        }

        .login-form-container {
            width: 100%;
            max-width: 420px;
            background: white;
            border-radius: 16px;
            padding: 48px 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin: auto;
        }

        .login-form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-form-header .brand-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin: 0 auto 24px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .login-form-header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0 0 8px 0;
        }

        .login-form-header p {
            font-size: 16px;
            color: #6b7280;
            margin: 0;
        }

        .login-form {
            margin: 2rem 0;
        }

        .form-group {
            margin-bottom: 24px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input[type="text"],
        .form-group input[type="password"] {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
            color: #1a1a1a;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input::placeholder {
            color: #9ca3af;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            margin: 20px 0;
        }

        .checkbox-label input[type="checkbox"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .checkbox-label input[type="checkbox"]:checked {
            background: #667eea;
            border-color: #667eea;
        }

        .checkbox-label input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .login-btn {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 32px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            outline: none;
            text-decoration: none;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .login-btn:focus {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
            color: white !important;
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .login-status {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #ef4444;
            min-height: 20px;
        }

        .login-footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        .login-footer p {
            color: #9ca3af;
            font-size: 12px;
            margin: 0;
        }

        /* Ensure no white backgrounds on any elements */
        .login-btn:visited {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .login-btn:link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        /* Remove any default button styling */
        button {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        button:focus {
            outline: none;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .login-status {
            margin-top: 1rem;
            padding: 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            min-height: 20px;
        }

        .login-status.error {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .login-status.success {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .login-status.loading {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* User Info Bar */
        .user-info-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-welcome {
            font-size: 1rem;
            font-weight: 500;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
        }

        .user-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .logout-btn {
            background: rgba(255, 107, 107, 0.2);
            border-color: rgba(255, 107, 107, 0.3);
        }

        .logout-btn:hover {
            background: rgba(255, 107, 107, 0.3);
        }

        .clear-btn {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .clear-btn:hover {
            background: rgba(255, 107, 107, 0.2);
            border-color: #ff6b6b;
        }

        /* Settings Section Styles */
        .settings-section {
            margin-top: 32px;
        }

        .settings-group {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f1f3f4;
        }

        .settings-group-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 20px 0;
            padding-bottom: 12px;
            border-bottom: 2px solid #f1f3f4;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .setting-item:last-child {
            margin-bottom: 0;
        }

        .setting-info {
            flex: 1;
        }

        .setting-name {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .setting-desc {
            display: block;
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
            flex-shrink: 0;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.3s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #4ecdc4;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }

        .setting-select {
            min-width: 160px;
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: border-color 0.2s ease;
        }

        .setting-select:focus {
            outline: none;
            border-color: #4ecdc4;
        }

<<<<<<< HEAD
        /* Shortcut Settings Styles */
        .shortcut-config {
            width: 100%;
            margin-top: 16px;
        }

        .shortcut-display {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .shortcut-label {
            font-size: 14px;
            font-weight: 500;
            color: #495057;
        }

        .shortcut-keys {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 14px;
            font-weight: 600;
            color: #007bff;
            background: white;
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .shortcut-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }

        .shortcut-btn {
            padding: 10px 16px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #007bff;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .shortcut-btn:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .shortcut-btn.secondary {
            background: white;
            color: #6c757d;
            border-color: #6c757d;
        }

        .shortcut-btn.secondary:hover {
            background: #6c757d;
            color: white;
        }

        .shortcut-btn.primary {
            background: #28a745;
            border-color: #28a745;
        }

        .shortcut-btn.primary:hover {
            background: #218838;
            border-color: #218838;
        }

        .shortcut-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #6c757d;
            border-color: #6c757d;
        }

        .shortcut-btn.listening {
            background: #ffc107;
            border-color: #ffc107;
            color: #212529;
            animation: pulse-listening 1.5s infinite;
        }

        @keyframes pulse-listening {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .shortcut-status {
            min-height: 24px;
            font-size: 13px;
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .shortcut-status.listening {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .shortcut-status.error {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .shortcut-status.success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .shortcut-actions {
            display: flex;
            justify-content: flex-end;
        }

        /* Toast Animation Styles */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        /* Activity Log Styles */
        .activity-section {
            margin-top: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 16px 0;
        }

        .activity-date {
            font-size: 12px;
            font-weight: 600;
            color: #ff8c00;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 24px 0 12px 0;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-time {
            font-size: 13px;
            color: #666;
            width: 80px;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
            margin-left: 16px;
        }

        .activity-text {
            font-size: 14px;
            color: #1a1a1a;
            margin: 0;
        }

        .activity-silent {
            font-size: 14px;
            color: #999;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .info-icon {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #e9ecef;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
        }

        /* Action Button */
        .action-btn {
            background: #343a40;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: auto;
        }

        .action-btn:hover {
            background: #23272b;
        }

        /* Sidebar Styles */
        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .brand-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .brand-name {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0;
        }

        .brand-plan {
            font-size: 11px;
            color: #666;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto; /* Allow sidebar nav to scroll if needed */
            max-height: calc(100vh - 200px); /* Reserve space for header and footer */
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #495057;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-item:hover {
<<<<<<< HEAD
            background: #1a1a1a;
            color: #ffffff;
        }

        .nav-item.active {
            background: #1a1a1a;
            color: white;
        }



=======
            background: #e9ecef;
            color: #1a1a1a;
        }

        .nav-item.active {
            background: #007bff;
            color: white;
        }

>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        .nav-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .upgrade-card {
<<<<<<< HEAD
            background: #1a1a1a;
=======
            background: linear-gradient(135deg, #007bff, #0056b3);
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            border-radius: 12px;
            padding: 16px;
            color: white;
            text-align: center;
            margin-bottom: 16px;
        }

        .upgrade-title {
            font-size: 13px;
            font-weight: 600;
            margin: 0 0 4px 0;
        }

        .upgrade-subtitle {
            font-size: 11px;
            opacity: 0.9;
            margin: 0 0 12px 0;
        }

        .upgrade-btn {
            background: white;
<<<<<<< HEAD
            color: #1a1a1a;
=======
            color: #007bff;
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
        }

        .sidebar-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .sidebar-action {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            color: #666;
            text-decoration: none;
            font-size: 13px;
            border: none;
            background: none;
            cursor: pointer;
            text-align: left;
        }

        .sidebar-action:hover {
            color: #1a1a1a;
        }

        /* Custom Scrollbar Styles */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .sidebar-nav::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* Smooth scrolling */
        .main-content {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <!-- <div class="minimize-hint">
        This window can be minimized to system tray
    </div> -->

    <!-- Loading Container -->
    <div class="loading-container" id="loadingContainer">
        <div class="loading-content">
            <div class="logo">EV</div>
            <h1>Easy Voice</h1>
            <p class="subtitle">AI-Powered Voice Transcription Assistant</p>

            <div class="loading-spinner"></div>
            <div class="loading-status" id="loadingStatus">Checking authentication...</div>
        </div>
    </div>

    <!-- Login Container -->
    <div class="login-container" id="loginContainer" style="display: none;">
        <div class="login-form-container">
            <div class="login-form-header">
                <div class="brand-logo">EV</div>
                <h1>Easy Voice</h1>
                <p>Sign in to your account</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" placeholder="Enter your username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" placeholder="Enter your password" required>
                </div>
                <label class="checkbox-label">
                    <input type="checkbox" id="rememberMe" checked>
                    <span>Remember me for 30 days</span>
                </label>
                <button type="submit" class="login-btn">Sign In</button>
            </form>

            <div class="login-status" id="loginStatus"></div>

            <div class="login-footer">
                <p>Easy Voice v1.0 - Secure voice transcription</p>
            </div>
        </div>
    </div>

    <!-- Main App Container -->
    <div class="app-layout" id="mainContainer" style="display: none;">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <div class="brand-logo">EV</div>
                    <div>
                        <h1 class="brand-name">Easy Voice</h1>
                        <span class="brand-plan">Basic</span>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <button class="nav-item active" onclick="showHome()">
                    <span class="nav-icon">🏠</span>
                    Home
                </button>
<<<<<<< HEAD
                <!-- <button class="nav-item" onclick="showDictionary()">
                    <span class="nav-icon">📖</span>
                    Dictionary
                </button> -->
=======
                <button class="nav-item" onclick="showDictionary()">
                    <span class="nav-icon">📖</span>
                    Dictionary
                </button>
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
                <button class="nav-item" onclick="showSettings()">
                    <span class="nav-icon">⚙️</span>
                    Settings
                </button>
            </nav>

            <div class="sidebar-footer">
                <div class="upgrade-card">
                    <h3 class="upgrade-title">Try Easy Voice Pro</h3>
                    <p class="upgrade-subtitle">Upgrade for unlimited dictation and other pro features</p>
                    <button class="upgrade-btn" onclick="showUpgrade()">Learn more</button>
                </div>

                <div class="sidebar-actions">
<<<<<<< HEAD
                    <!-- <button class="sidebar-action" onclick="addTeam()">
                        <span class="nav-icon">👥</span>
                        Add your team
                    </button> -->
                    <!-- <button class="sidebar-action" onclick="referFriend()">
                        <span class="nav-icon">🔗</span>
                        Refer a friend
                    </button> -->
=======
                    <button class="sidebar-action" onclick="addTeam()">
                        <span class="nav-icon">👥</span>
                        Add your team
                    </button>
                    <button class="sidebar-action" onclick="referFriend()">
                        <span class="nav-icon">🔗</span>
                        Refer a friend
                    </button>
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
                    <button class="sidebar-action" onclick="showHelp()">
                        <span class="nav-icon">❓</span>
                        Help
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="container">
                <!-- Header -->
                <div class="header">
<<<<<<< HEAD
                    <div class="workspace-header">
                        <div class="workspace-info">
                            <div class="workspace-label">My Workspace</div>
                            <h1 class="welcome-title">Good evening, <span id="userDisplayName">Frank</span></h1>
                        </div>
                        <div class="header-controls">
                            <button onclick="showUserPlan()" class="btn-secondary">Plan Details</button>
                            <button onclick="logout()" class="btn-secondary">Logout</button>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-label">Current Plan</span>
                            </div>
                            <div class="stat-value" id="sessionTime">Basic Plan</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-label">Usage Progress</span>
                            </div>
                            <div class="stat-value-with-circle">
                                <div class="circular-progress-container">
                                    <div class="circular-progress" id="circularProgress">
                                        <svg class="progress-ring" width="60" height="60">
                                            <circle class="progress-ring-circle-bg" cx="30" cy="30" r="25" />
                                            <circle class="progress-ring-circle" cx="30" cy="30" r="25" id="progressCircle" />
                                        </svg>
                                        <div class="progress-text" id="usagePercentage">0%</div>
                                    </div>
                                </div>
                                <div class="usage-text">
                                    <span id="wordCount">Usage</span>
                                </div>
                            </div>
                        </div>
                    </div>


=======
                    <h1 class="welcome-title">Welcome back, <span id="userDisplayName">User</span></h1>
                    <div class="stats-bar">
                        <div class="stat-item">
                            <div class="stat-icon orange"></div>
                            <span id="sessionTime">3 weeks</span>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon red"></div>
                            <span id="wordCount">192 words</span>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon yellow"></div>
                            <span id="wpmCount">64 WPM</span>
                        </div>
                        <div style="margin-left: auto;">
                            <button onclick="showUserPlan()" class="btn-secondary">Plan Details</button>
                            <button onclick="logout()" class="btn-secondary" style="margin-left: 8px;">Logout</button>
                        </div>
                    </div>
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
                </div>

        <!-- Main Card -->
        <div class="main-card">
            <h2 class="card-title">Voice dictation in any app</h2>
            <p class="card-subtitle">Hold down the trigger keys <span class="hotkey-display"><span class="key">Ctrl</span> + <span class="key">Alt</span></span> and speak into any textbox</p>
            <button onclick="testRecording()" class="action-btn">Explore use cases</button>
        </div>

        <!-- Activity Section -->
        <div class="activity-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h2 class="section-title" style="margin: 0;">Recent activity</h2>
                <button onclick="clearActivityHistory()" class="clear-btn" title="Clear activity history">
                    🗑️ Clear
                </button>
            </div>

            <div class="activity-date">TODAY</div>

            <div id="activityList">
                <!-- Activity items will be populated by JavaScript -->
            </div>
        </div>



        <!-- Settings Section -->
        <div id="settingsSection" class="settings-section" style="display: none;">
            <h2 class="section-title">Settings</h2>

            <!-- Translation Settings -->
            <div class="settings-group">
                <h3 class="settings-group-title">🌐 Translation Settings</h3>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="setting-name">Enable Translation</span>
                        <span class="setting-desc">Automatically translate transcriptions to your preferred language</span>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="translationEnabled" onchange="toggleTranslation()">
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div class="setting-item" id="languageSettings" style="display: none;">
                    <div class="setting-info">
                        <span class="setting-name">Target Language</span>
                        <span class="setting-desc">Select your preferred language for translations</span>
                    </div>
                    <select id="targetLanguage" onchange="saveTranslationSettings()" class="setting-select">
                        <option value="en">English</option>
<<<<<<< HEAD
=======
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese (Simplified)</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                        <option value="th">Thai</option>
                        <option value="vi">Vietnamese</option>
                        <option value="nl">Dutch</option>
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
                    </select>
                </div>
            </div>

<<<<<<< HEAD
            <!-- Shortcut Settings -->
            <div class="settings-group">
                <h3 class="settings-group-title">🎹 Recording Shortcut</h3>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="setting-name">Recording Shortcut Keys</span>
                        <span class="setting-desc">Choose keys to start/stop recording</span>
                    </div>
                </div>

                <div class="shortcut-config">
                    <div class="shortcut-display">
                        <span class="shortcut-label">Current Shortcut:</span>
                        <span class="shortcut-keys" id="currentShortcut">Ctrl + Alt</span>
                    </div>

                    <div class="shortcut-controls">
                        <button class="shortcut-btn" id="changeShortcutBtn">Click to Change Shortcut</button>
                        <button class="shortcut-btn secondary" id="resetShortcutBtn">Reset to Default</button>
                    </div>

                    <div class="shortcut-status" id="shortcutStatus"></div>

                    <div class="shortcut-actions">
                        <button class="shortcut-btn primary" id="saveShortcutBtn" disabled>Save Changes</button>
                    </div>
                </div>
            </div>

=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            <!-- General Settings -->
            <div class="settings-group">
                <h3 class="settings-group-title">⚙️ General Settings</h3>

<<<<<<< HEAD
=======


>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
                <div class="setting-item">
                    <div class="setting-info">
                        <span class="setting-name">Minimize to Tray</span>
                        <span class="setting-desc">Keep Easy Voice running in the system tray when closed</span>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="minimizeToTray" checked disabled>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- <div class="buttons">
            <button class="primary" onclick="testRecording()">Test Recording</button>
            <button onclick="showWidget()">Show Widget</button>
            <button onclick="openSettings()">Settings</button>
            <button onclick="minimizeToTray()">Minimize to Tray</button>
        </div> -->

                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #f1f3f4; text-align: center;">
                    <p style="font-size: 12px; color: #999; margin: 0;">Easy Voice v1.0 - Powered by AI</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        // Global variables
        let currentUser = null;
        let tokenUsage = 0;

        // Prevent zoom shortcuts
        document.addEventListener('keydown', function(event) {
            // Prevent Ctrl+Plus, Ctrl+Minus, Ctrl+0 (zoom shortcuts)
            if (event.ctrlKey && (event.key === '=' || event.key === '-' || event.key === '0')) {
                event.preventDefault();
                return false;
            }
        });

        // Prevent zoom with mouse wheel
        document.addEventListener('wheel', function(event) {
            if (event.ctrlKey) {
                event.preventDefault();
                return false;
            }
        }, { passive: false });

        // Initialize app
        document.addEventListener('DOMContentLoaded', async () => {
            await initializeApp();
        });

        // Initialize app with proper loading flow
        async function initializeApp() {
            try {
                // Show loading screen
                showLoadingScreen();

                // Update loading status
                updateLoadingStatus('Checking saved credentials...');

                // Small delay to show loading screen
                await new Promise(resolve => setTimeout(resolve, 500));

                // Check for existing login
                const savedAuth = await ipcRenderer.invoke('get-saved-auth');

                if (savedAuth && savedAuth.user) {
                    currentUser = savedAuth.user;

                    if (currentUser.isActive) {
                        updateLoadingStatus('Auto-login successful! Loading dashboard...');
                        // Small delay to show success message
                        await new Promise(resolve => setTimeout(resolve, 800));
                        showMainApp();
                        return;
                    } else {
                        updateLoadingStatus('Subscription expired! Loading dashboard...');
                        // Small delay to show message
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        showMainApp();
                        return;
                    }
                }

                // No valid login found, show login form
                updateLoadingStatus('Please login to continue...');
                await new Promise(resolve => setTimeout(resolve, 500));
                showLoginForm();

            } catch (error) {
                console.error('Error during app initialization:', error);
                updateLoadingStatus('Error loading app, please login...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                showLoginForm();
            }
        }

        function showLoadingScreen() {
            document.getElementById('loadingContainer').style.display = 'block';
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('mainContainer').style.display = 'none';
        }

        function updateLoadingStatus(message) {
            const statusElement = document.getElementById('loadingStatus');
            if (statusElement) {
                statusElement.textContent = message;
            }
        }

        // Login form submission
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await handleLogin();
        });

        async function handleLogin() {
            const email = document.getElementById('username').value; // Using email field
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const statusDiv = document.getElementById('loginStatus');
            const loginBtn = document.querySelector('.login-btn');

            // Show loading state
            loginBtn.disabled = true;
            loginBtn.textContent = 'Logging in...';
            statusDiv.className = 'login-status loading';
            statusDiv.textContent = 'Authenticating...';

            try {
                console.log('Attempting login with email:', email);

                // Use the IPC handler for login
                const result = await ipcRenderer.invoke('login', email, password);
                console.log('Login result:', result);

                if (result.success) {
                    currentUser = result.user;

                    // Save credentials if remember me is checked
                    if (rememberMe) {
                        await ipcRenderer.invoke('save-auth', {
                            user: currentUser,
                            credentials: { username: email, password }
                        });
                    }

                    // Check if user is active
                    if (currentUser.isActive) {
                        statusDiv.className = 'login-status success';
                        statusDiv.textContent = 'Login successful! Loading dashboard...';

                        setTimeout(() => {
                            showMainApp();
                        }, 1200);
                    } else {
                        statusDiv.className = 'login-status error';
                        statusDiv.textContent = 'Your subscription has expired. Please recharge to continue using the service.';

                        // Still show main app but with limited functionality
                        setTimeout(() => {
                            showMainApp();
                        }, 2000);
                    }
                } else {
                    console.error('Login failed:', result);
                    throw new Error(result.message || 'Invalid email or password');
                }
            } catch (error) {
                console.error('Login error:', error);
                statusDiv.className = 'login-status error';

                // Provide more specific error messages
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    statusDiv.textContent = 'Network error. Please check your internet connection.';
                } else if (error.message.includes('Invalid username')) {
                    statusDiv.textContent = 'Invalid username or password. Please try again.';
                } else {
                    statusDiv.textContent = error.message || 'Login failed. Please try again.';
                }

                loginBtn.disabled = false;
                loginBtn.textContent = 'Login';
            }
        }



        function showLoginForm() {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'block';
            document.getElementById('mainContainer').style.display = 'none';

            // Update status to show login required
            const statusElement = document.getElementById('appStatus');
            const statusDot = document.querySelector('.status-dot');
            if (statusElement) {
                statusElement.textContent = 'Please log in to use recording features';
            }
            if (statusDot) {
                statusDot.className = 'status-dot disabled';
            }
        }

        function showMainApp() {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('mainContainer').style.display = 'block';

            if (currentUser) {
                // Update user display name
                document.getElementById('userDisplayName').textContent = currentUser.name || currentUser.email;

                // Show subscription expired warning if user is inactive
                showSubscriptionStatus();

                // Update stats with API data
                updateUserStats();

                // Load activity data
                loadActivityData();
            }
        }

        function showSubscriptionStatus() {
            // Remove any existing warning
            const existingWarning = document.getElementById('subscriptionWarning');
            if (existingWarning) {
                existingWarning.remove();
            }

            // Show warning if user is inactive
            if (currentUser && !currentUser.isActive) {
                const warningDiv = document.createElement('div');
                warningDiv.id = 'subscriptionWarning';
                warningDiv.style.cssText = `
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                    color: white;
                    padding: 12px 20px;
                    margin: 10px 20px;
                    border-radius: 10px;
                    text-align: center;
                    font-weight: bold;
                    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
                    animation: pulse 2s infinite;
                `;
                warningDiv.innerHTML = `
                    ⚠️ Your subscription has expired! Recording features are disabled.
                    <strong>Please recharge to continue using the service.</strong>
                `;

                // Insert after the header
                const header = document.querySelector('.header');
                if (header) {
                    header.parentNode.insertBefore(warningDiv, header.nextSibling);
                }
            }
        }

        function updateUserStats() {
            if (!currentUser) return;

            // Update tokens used
            const wordCountElement = document.getElementById('wordCount');
            if (wordCountElement) {
                wordCountElement.textContent = `${currentUser.tokensUsed} tokens`;
            }

            // Update plan - show expired message if inactive
            const sessionTimeElement = document.getElementById('sessionTime');
            if (sessionTimeElement) {
                if (currentUser.isActive) {
                    const planDisplay = currentUser.plan.charAt(0).toUpperCase() + currentUser.plan.slice(1);
                    sessionTimeElement.textContent = `${planDisplay} Plan`;
                } else {
                    sessionTimeElement.textContent = `Token Expired - Recharge`;
                    sessionTimeElement.style.color = '#ff6b6b';
                    sessionTimeElement.style.fontWeight = 'bold';
                }
            }

            // Update cost or weekly usage
            const wpmElement = document.getElementById('wpmCount');
            if (wpmElement) {
                if (currentUser.isActive) {
                    wpmElement.textContent = `${currentUser.weeklyTokens}/${currentUser.weeklyLimit} weekly`;
                } else {
                    wpmElement.textContent = `Service Suspended`;
                    wpmElement.style.color = '#ff6b6b';
                }
            }
<<<<<<< HEAD

            // Update progress bar
            updateUsageProgressBar();
        }

        function updateUsageProgressBar() {
            const percentageSpan = document.getElementById('usagePercentage');
            const progressCircle = document.getElementById('progressCircle');
            const wordCountSpan = document.getElementById('wordCount');

            if (!currentUser) return;

            // Check if user has unlimited plan (no weekly limit or very high limit)
            const hasUnlimitedPlan = !currentUser.weeklyLimit || currentUser.weeklyLimit >= 999999;

            if (hasUnlimitedPlan) {
                // For unlimited users, show different display
                if (percentageSpan) percentageSpan.textContent = '∞';
                if (wordCountSpan) wordCountSpan.textContent = 'Unlimited Plan';
                return;
            }

            // Calculate usage percentage
            const usagePercentage = Math.min(100, Math.round((currentUser.weeklyTokens / currentUser.weeklyLimit) * 100));

            // Update percentage text in circular progress
            if (percentageSpan) {
                percentageSpan.textContent = `${usagePercentage}%`;
            }

            // Update usage text display
            if (wordCountSpan) {
                wordCountSpan.textContent = 'Usage';
            }

            // Update circular progress bar
            if (progressCircle) {
                const circumference = 2 * Math.PI * 25; // radius = 25
                const offset = circumference - (usagePercentage / 100) * circumference;
                setTimeout(() => {
                    progressCircle.style.strokeDashoffset = offset;
                }, 100);
            }

            // Update colors based on status and percentage
            if (!currentUser.isActive) {
                // Expired subscription
                if (progressCircle) progressCircle.className = 'progress-ring-circle danger';
                if (progressCircle) progressCircle.style.strokeDashoffset = 0;
                if (percentageSpan) percentageSpan.textContent = '100%';
            } else {
                // Active subscription - color based on usage
                if (usagePercentage <= 50) {
                    if (progressCircle) progressCircle.className = 'progress-ring-circle';
                } else if (usagePercentage <= 80) {
                    if (progressCircle) progressCircle.className = 'progress-ring-circle warning';
                } else {
                    if (progressCircle) progressCircle.className = 'progress-ring-circle danger';
                }
            }
=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        }

        function loadActivityData() {
            const activityList = document.getElementById('activityList');

            // Load activities from localStorage
            const activities = getActivitiesFromLocalStorage();

            if (activities.length === 0) {
                // Show empty state
                activityList.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-content">
                            <p class="activity-text" style="text-align: center; color: #666; font-style: italic;">
                                No recent activity. Start recording to see your transcriptions here.
                            </p>
                        </div>
                    </div>
                `;
                return;
            }

            activityList.innerHTML = activities.map(activity => {
                if (activity.type === 'silent') {
                    return `
                        <div class="activity-item">
                            <div class="activity-time">${activity.time}</div>
                            <div class="activity-content">
                                <p class="activity-silent">${activity.text} <span class="info-icon">?</span></p>
                            </div>
                        </div>
                    `;
                } else {
                    return `
                        <div class="activity-item">
                            <div class="activity-time">${activity.time}</div>
                            <div class="activity-content">
                                <p class="activity-text">${activity.text}</p>
                            </div>
                        </div>
                    `;
                }
            }).join('');
        }

        // localStorage functions for activity management
        function getActivitiesFromLocalStorage() {
            try {
                const stored = localStorage.getItem('voiceActivities');
                if (!stored) return [];

                const activities = JSON.parse(stored);

                // Convert stored activities to display format
                return activities.map(activity => ({
                    time: formatTimeFromTimestamp(activity.timestamp),
                    text: activity.text || 'Audio is silent.',
                    type: activity.type || 'transcription'
                })).slice(0, 20); // Limit to last 20 items
            } catch (error) {
                console.error('Error loading activities from localStorage:', error);
                return [];
            }
        }

        function saveActivityToLocalStorage(data) {
            try {
                const stored = localStorage.getItem('voiceActivities');
                let activities = stored ? JSON.parse(stored) : [];

                // Add new activity to the beginning
                activities.unshift({
                    timestamp: data.timestamp,
                    text: data.text,
                    type: data.type,
                    processingTime: data.processingTime
                });

                // Keep only last 50 items to prevent localStorage from growing too large
                activities = activities.slice(0, 50);

                localStorage.setItem('voiceActivities', JSON.stringify(activities));
                console.log('📝 Activity saved to localStorage:', data.text.substring(0, 50) + '...');
            } catch (error) {
                console.error('Error saving activity to localStorage:', error);
            }
        }

        function formatTimeFromTimestamp(timestamp) {
            try {
                const date = new Date(timestamp);
                return date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
            } catch (error) {
                return new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
            }
        }

        function clearActivityHistory() {
            try {
                localStorage.removeItem('voiceActivities');
                loadActivityData(); // Reload to show empty state
                console.log('🗑️ Activity history cleared');
            } catch (error) {
                console.error('Error clearing activity history:', error);
            }
        }

        // Settings Section Functions
        function showSettings() {
            setActiveNavItem('Settings');

            // Hide main dashboard content
            document.querySelector('.main-card').style.display = 'none';
            document.querySelector('.activity-section').style.display = 'none';

            // Show settings section
            document.getElementById('settingsSection').style.display = 'block';

            // Load settings
            loadTranslationSettings();
<<<<<<< HEAD
            initializeShortcutSettings();
=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        }

        function showHome() {
            setActiveNavItem('Home');

            // Show main dashboard content
            document.querySelector('.main-card').style.display = 'block';
            document.querySelector('.activity-section').style.display = 'block';

            // Hide settings section
            document.getElementById('settingsSection').style.display = 'none';
        }

        function toggleTranslation() {
            const enabled = document.getElementById('translationEnabled').checked;
            const languageSettings = document.getElementById('languageSettings');
<<<<<<< HEAD
            const targetLanguage = document.getElementById('targetLanguage');

            if (enabled) {
                languageSettings.style.display = 'flex';
                // Set English as default when translation is enabled
                targetLanguage.value = 'en';
=======

            if (enabled) {
                languageSettings.style.display = 'flex';
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            } else {
                languageSettings.style.display = 'none';
            }

            saveTranslationSettings();
        }

        function saveTranslationSettings() {
            try {
                const enabled = document.getElementById('translationEnabled').checked;
                const language = document.getElementById('targetLanguage').value;

                const settings = {
                    enabled: enabled,
                    language: language
                };

                localStorage.setItem('translationSettings', JSON.stringify(settings));
                console.log('💾 Translation settings saved:', settings);
            } catch (error) {
                console.error('Error saving translation settings:', error);
            }
        }

        function loadTranslationSettings() {
            try {
                const settings = JSON.parse(localStorage.getItem('translationSettings') || '{}');

                // Set checkbox
                document.getElementById('translationEnabled').checked = settings.enabled || false;

                // Set language
<<<<<<< HEAD
                document.getElementById('targetLanguage').value = settings.language || 'en';
=======
                document.getElementById('targetLanguage').value = settings.language || 'es';
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7

                // Show/hide dependent settings
                const enabled = settings.enabled || false;
                const languageSettings = document.getElementById('languageSettings');

                if (enabled) {
                    languageSettings.style.display = 'flex';
                } else {
                    languageSettings.style.display = 'none';
                }

                console.log('📋 Translation settings loaded:', settings);
            } catch (error) {
                console.error('Error loading translation settings:', error);
            }
        }

<<<<<<< HEAD
        // Shortcut Settings Functions
        let shortcutState = {
            current: { ctrl: true, alt: true, shift: false, key: null },
            pending: null,
            listening: false,
            hasChanges: false
        };

        const forbiddenCombinations = [
            // Common system shortcuts
            { ctrl: true, key: 'c' }, { ctrl: true, key: 'v' }, { ctrl: true, key: 'x' },
            { ctrl: true, key: 'z' }, { ctrl: true, key: 'y' }, { ctrl: true, key: 'a' },
            { ctrl: true, key: 's' }, { ctrl: true, key: 'n' }, { ctrl: true, key: 'o' },
            // Window management
            { alt: true, key: 'Tab' }, { alt: true, key: 'F4' },
            // System critical
            { ctrl: true, alt: true, key: 'Delete' }
        ];

        function initializeShortcutSettings() {
            loadShortcutSettings();
            setupShortcutEventListeners();
        }

        function loadShortcutSettings() {
            try {
                const saved = JSON.parse(localStorage.getItem('recordingShortcut') || '{}');
                if (saved.ctrl !== undefined) {
                    shortcutState.current = saved;
                }
                updateShortcutDisplay();
            } catch (error) {
                console.error('Error loading shortcut settings:', error);
            }
        }

        function setupShortcutEventListeners() {
            const changeBtn = document.getElementById('changeShortcutBtn');
            const resetBtn = document.getElementById('resetShortcutBtn');
            const saveBtn = document.getElementById('saveShortcutBtn');

            if (changeBtn) changeBtn.addEventListener('click', startListeningForShortcut);
            if (resetBtn) resetBtn.addEventListener('click', resetToDefault);
            if (saveBtn) saveBtn.addEventListener('click', saveShortcutSettings);
        }

        function updateShortcutDisplay() {
            const display = document.getElementById('currentShortcut');
            if (!display) return;

            const shortcut = shortcutState.current;
            let keys = [];
            if (shortcut.ctrl) keys.push('Ctrl');
            if (shortcut.alt) keys.push('Alt');
            if (shortcut.shift) keys.push('Shift');
            if (shortcut.key) keys.push(shortcut.key.toUpperCase());

            display.textContent = keys.join(' + ');
            updateShortcutReferences(keys.join(' + '));
        }

        function updateShortcutReferences(shortcutText) {
            // Update main card subtitle
            const cardSubtitle = document.querySelector('.card-subtitle');
            if (cardSubtitle) {
                const keys = shortcutText.split(' + ');
                const keySpans = keys.map(key => `<span class="key">${key}</span>`).join(' + ');
                cardSubtitle.innerHTML = `Hold down the trigger keys <span class="hotkey-display">${keySpans}</span> and speak into any textbox`;
            }
        }

        function startListeningForShortcut() {
            const changeBtn = document.getElementById('changeShortcutBtn');
            const statusDiv = document.getElementById('shortcutStatus');

            shortcutState.listening = true;
            shortcutState.pending = { ctrl: false, alt: false, shift: false, key: null };

            changeBtn.textContent = 'Listening... Press your keys';
            changeBtn.classList.add('listening');
            changeBtn.disabled = true;

            statusDiv.textContent = 'Press your desired key combination...';
            statusDiv.className = 'shortcut-status listening';

            // Add global keydown listener
            document.addEventListener('keydown', handleShortcutKeyDown, true);
            document.addEventListener('keyup', handleShortcutKeyUp, true);
        }

        function handleShortcutKeyDown(event) {
            if (!shortcutState.listening) return;

            event.preventDefault();
            event.stopPropagation();

            const pending = shortcutState.pending;

            // Track modifier keys
            if (event.key === 'Control') pending.ctrl = true;
            else if (event.key === 'Alt') pending.alt = true;
            else if (event.key === 'Shift') pending.shift = true;
            else if (event.key.length === 1 || ['Tab', 'Delete', 'Backspace', 'Enter'].includes(event.key)) {
                pending.key = event.key.toLowerCase();
            }

            // Update real-time display
            updatePendingDisplay();
        }

        function handleShortcutKeyUp(event) {
            if (!shortcutState.listening) return;

            event.preventDefault();
            event.stopPropagation();

            // When all keys are released, validate the combination
            setTimeout(() => {
                if (shortcutState.listening) {
                    validateAndSetShortcut();
                }
            }, 100);
        }

        function updatePendingDisplay() {
            const statusDiv = document.getElementById('shortcutStatus');
            const pending = shortcutState.pending;

            let keys = [];
            if (pending.ctrl) keys.push('Ctrl');
            if (pending.alt) keys.push('Alt');
            if (pending.shift) keys.push('Shift');
            if (pending.key) keys.push(pending.key.toUpperCase());

            if (keys.length > 0) {
                statusDiv.textContent = `Detected: ${keys.join(' + ')}`;
            }
        }

        function validateAndSetShortcut() {
            const pending = shortcutState.pending;
            const statusDiv = document.getElementById('shortcutStatus');

            // Check if we have at least one key
            const hasModifier = pending.ctrl || pending.alt || pending.shift;
            const hasKey = pending.key !== null;

            if (!hasModifier && !hasKey) {
                showShortcutError('Please press at least one key');
                return;
            }

            // Check against forbidden combinations
            if (isForbiddenCombination(pending)) {
                showShortcutError('❌ This combination is reserved by system');
                return;
            }

            // Valid combination
            shortcutState.pending = pending;
            shortcutState.hasChanges = true;

            let keys = [];
            if (pending.ctrl) keys.push('Ctrl');
            if (pending.alt) keys.push('Alt');
            if (pending.shift) keys.push('Shift');
            if (pending.key) keys.push(pending.key.toUpperCase());

            statusDiv.textContent = `✅ New shortcut: ${keys.join(' + ')}`;
            statusDiv.className = 'shortcut-status success';

            // Enable save button
            document.getElementById('saveShortcutBtn').disabled = false;

            stopListening();
        }

        function isForbiddenCombination(combo) {
            return forbiddenCombinations.some(forbidden => {
                return Object.keys(forbidden).every(key => {
                    if (key === 'key') {
                        return forbidden[key] === combo[key];
                    }
                    return !!forbidden[key] === !!combo[key];
                });
            });
        }

        function showShortcutError(message) {
            const statusDiv = document.getElementById('shortcutStatus');
            statusDiv.textContent = message;
            statusDiv.className = 'shortcut-status error';

            // Show toast notification
            showToast(message, 'error');

            setTimeout(() => {
                stopListening();
            }, 2000);
        }

        function stopListening() {
            const changeBtn = document.getElementById('changeShortcutBtn');

            shortcutState.listening = false;

            changeBtn.textContent = 'Click to Change Shortcut';
            changeBtn.classList.remove('listening');
            changeBtn.disabled = false;

            // Remove global listeners
            document.removeEventListener('keydown', handleShortcutKeyDown, true);
            document.removeEventListener('keyup', handleShortcutKeyUp, true);
        }

        function resetToDefault() {
            shortcutState.pending = { ctrl: true, alt: true, shift: false, key: null };
            shortcutState.hasChanges = true;

            const statusDiv = document.getElementById('shortcutStatus');
            statusDiv.textContent = '✅ Reset to default: Ctrl + Alt';
            statusDiv.className = 'shortcut-status success';

            // Enable save button
            document.getElementById('saveShortcutBtn').disabled = false;
        }

        async function saveShortcutSettings() {
            try {
                if (!shortcutState.hasChanges || !shortcutState.pending) return;

                // Save to localStorage
                localStorage.setItem('recordingShortcut', JSON.stringify(shortcutState.pending));

                // Update current state
                shortcutState.current = { ...shortcutState.pending };
                shortcutState.hasChanges = false;
                shortcutState.pending = null;

                // Update display
                updateShortcutDisplay();

                // Notify main process to update shortcut
                await ipcRenderer.invoke('update-recording-shortcut', shortcutState.current);

                // Reset UI
                const statusDiv = document.getElementById('shortcutStatus');
                statusDiv.textContent = '';
                statusDiv.className = 'shortcut-status';

                document.getElementById('saveShortcutBtn').disabled = true;

                showToast('✅ Shortcut saved successfully', 'success');

            } catch (error) {
                console.error('Error saving shortcut settings:', error);
                showToast('❌ Error saving shortcut', 'error');
            }
        }

        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                animation: slideInRight 0.3s ease;
            `;

            document.body.appendChild(toast);

            // Remove after 3 seconds
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        function updateStats() {
            // Update stats with real data from token tracker
            ipcRenderer.invoke('get-token-stats').then(stats => {
                document.getElementById('wordCount').textContent = `${stats.total || 0} tokens`;
            }).catch(error => {
                console.error('Error getting stats:', error);
            });
        }

        function addActivityItem(text, type = 'transcription') {
            const activityList = document.getElementById('activityList');
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            // Remove empty state if it exists
            const emptyState = activityList.querySelector('.activity-text[style*="text-align: center"]');
            if (emptyState) {
                activityList.innerHTML = '';
            }

            const newItem = document.createElement('div');
            newItem.className = 'activity-item';

            if (type === 'silent') {
                newItem.innerHTML = `
                    <div class="activity-time">${timeString}</div>
                    <div class="activity-content">
                        <p class="activity-silent">${text} <span class="info-icon">?</span></p>
                    </div>
                `;
            } else {
                newItem.innerHTML = `
                    <div class="activity-time">${timeString}</div>
                    <div class="activity-content">
                        <p class="activity-text">${text}</p>
                    </div>
                `;
            }

            // Add to top of list
            activityList.insertBefore(newItem, activityList.firstChild);

            // Keep only last 20 items in display
            while (activityList.children.length > 20) {
                activityList.removeChild(activityList.lastChild);
            }

            // Save to localStorage (this will be called from transcription-complete event)
            // Manual additions (like from test recording) should also be saved
            if (!text.includes('No recent activity')) {
                saveActivityToLocalStorage({
                    timestamp: now.toISOString(),
                    text: text,
                    type: type,
                    processingTime: 0
                });
            }
        }

        // Sidebar Navigation Functions

        function showDictionary() {
            setActiveNavItem('Dictionary');
            // TODO: Implement dictionary view
            console.log('Dictionary feature coming soon');
        }

        function showNotes() {
            setActiveNavItem('Notes');
            // TODO: Implement notes view
            console.log('Notes feature coming soon');
        }

        function setActiveNavItem(itemName) {
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to clicked item
            document.querySelectorAll('.nav-item').forEach(item => {
                if (item.textContent.trim() === itemName) {
                    item.classList.add('active');
                }
            });
        }

        // Sidebar Action Functions
        function showUpgrade() {
            console.log('Upgrade to Pro feature');
            // TODO: Implement upgrade flow
        }

        function addTeam() {
            console.log('Add team feature');
            // TODO: Implement team management
        }

        function referFriend() {
            console.log('Refer a friend feature');
            // TODO: Implement referral system
        }

        function showHelp() {
            console.log('Help feature');
<<<<<<< HEAD
            if (typeof require !== 'undefined') {
                // Electron environment - open in default browser
                const { shell } = require('electron');
                shell.openExternal('https://easyvoice.app');
            } else {
                // Web environment fallback
                window.open('https://easyvoice.app', '_blank');
            }
=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            // TODO: Implement help system
        }

        async function logout() {
            try {
                // Show loading screen briefly
                showLoadingScreen();
                updateLoadingStatus('Logging out...');

                await ipcRenderer.invoke('logout');
                currentUser = null;
                tokenUsage = 0;

                // Reset form
                document.getElementById('loginForm').reset();
                document.getElementById('loginStatus').textContent = '';
                document.getElementById('loginStatus').className = 'login-status';

                // Reset status message
                const statusElement = document.getElementById('appStatus');
                const statusDot = document.querySelector('.status-dot');
                if (statusElement) {
                    statusElement.textContent = 'Please log in to use recording features';
                }
                if (statusDot) {
                    statusDot.className = 'status-dot disabled';
                }

                // Small delay to show logout message
                await new Promise(resolve => setTimeout(resolve, 800));

                showLoginForm();
            } catch (error) {
                console.error('Logout error:', error);
                showLoginForm();
            }
        }

        function showUserPlan() {
            // Open plan details in external browser
            const { shell } = require('electron');
            shell.openExternal('http://easyvoice.app/myplan');
            console.log('Opening plan details in browser: http://easyvoice.app/myplan');
        }

        async function testRecording() {
            // Open use cases in external browser
            const { shell } = require('electron');
            shell.openExternal('http://easyvoice.app');
            console.log('Opening use cases in browser: http://easyvoice.app');
        }

        function showWidget() {
            console.log('Showing recording widget...');

            // Check if user is logged in and active
            if (!currentUser || !currentUser.userId) {
                alert('Please log in first to use recording features.');
                return;
            }

            if (!currentUser.isActive) {
                alert('Your subscription has expired. Please recharge to continue using recording features.');
                return;
            }

            ipcRenderer.send('show-recording-widget');
        }

        function openSettings() {
            console.log('Opening settings...');
            // You can implement settings window here
            alert('Settings panel coming soon!');
        }

        function minimizeToTray() {
            console.log('Minimizing to tray...');
            ipcRenderer.send('minimize-to-tray');
        }

        // Listen for app status updates
        ipcRenderer.on('recording-started', () => {
            const statusSpan = document.querySelector('.status span');
            const statusDot = document.querySelector('.status-dot');
            if (statusSpan) statusSpan.textContent = 'Recording in progress...';
            if (statusDot) statusDot.style.background = '#ff6b6b';
        });

        ipcRenderer.on('recording-stopped', () => {
            const statusSpan = document.querySelector('.status span');
            const statusDot = document.querySelector('.status-dot');
            if (statusSpan) statusSpan.textContent = 'Ready and listening for hotkeys';
            if (statusDot) statusDot.style.background = '#4ecdc4';
        });

        // Listen for token usage updates
        ipcRenderer.on('token-used', (event, tokens) => {
            tokenUsage += tokens;
            console.log(`Token usage updated: +${tokens} tokens (Total: ${tokenUsage})`);

<<<<<<< HEAD
            // Update current user token counts
            if (currentUser) {
                currentUser.tokensUsed += tokens;
                currentUser.weeklyTokens += tokens;
            }

=======
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
            // Send token usage to API
            if (currentUser && currentUser.userId) {
                sendTokenUsage(tokens);
            }

<<<<<<< HEAD
            // Update stats display and progress bar
            updateStats();
            updateUsageProgressBar();
=======
            // Update stats display
            updateStats();
>>>>>>> 781424c4c3c01e225823a10e17a48ebf5f0442a7
        });

        // Listen for login required message
        ipcRenderer.on('show-login-required', () => {
            if (document.getElementById('loginContainer').style.display === 'none') {
                alert('Please log in to use recording features. The app will now show the login screen.');
                showLoginForm();
            }
        });

        // Listen for transcription completion
        ipcRenderer.on('transcription-complete', (event, data) => {
            console.log('📝 Transcription completed:', data);

            // Add to activity list
            addActivityItem(data.text, data.type);

            // Save to localStorage
            saveActivityToLocalStorage(data);
        });

        // Send token usage to tracking API via IPC
        async function sendTokenUsage(tokensUsed) {
            try {
                console.log(`📊 Sending token usage: ${tokensUsed} tokens for user ${currentUser.email} (ID: ${currentUser.userId})`);

                // Use IPC handler to update token usage
                const result = await ipcRenderer.invoke('update-token-usage', tokensUsed);

                if (result.success) {
                    console.log('✅ Token usage tracked successfully');
                    // Update current user with the latest data from backend
                    if (result.user) {
                        currentUser = result.user;
                        updateUserStats();
                    }
                } else {
                    console.warn('❌ Failed to track token usage:', result.message);
                    // The backend already handles fallback updates
                    if (result.user) {
                        currentUser = result.user;
                        updateUserStats();
                    }
                }
            } catch (error) {
                console.error('❌ Error tracking token usage:', error);
                // Update locally as final fallback
                if (currentUser) {
                    currentUser.tokensUsed += tokensUsed;
                    currentUser.weeklyTokens += tokensUsed;
                    updateUserStats();
                }
            }
        }

        // Show welcome message
        setTimeout(() => {
            console.log('VoiceA is ready! Use Ctrl+Alt to start recording.');
        }, 1000);
    </script>
</body>
</html>